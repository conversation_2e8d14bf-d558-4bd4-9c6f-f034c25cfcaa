<script>
	import { t } from '$lib/stores/i18n';
	import { enhance } from '$app/forms';
	import { Button, Alert } from 'flowbite-svelte';
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';
	import { onMount } from 'svelte';

	// props passed in from the page load
	export let businessHourSettings;

	// Initialize days and toggle state
	let days = [];
	let sameAsBusinessHours = true;

	// Initial state tracking for change detection
	let initialDays = [];
	let initialSameAsBusinessHours = true;
	let initialCleanedDays = [];

	// Error handling state
	let showErrorMessage = false;
	let errorMessage = '';
	let fieldErrors = {};

	// Function to parse error messages (supports both single string and multi-field errors)
	function parseErrorMessages(error) {
		if (!error) return { fieldErrors: {}, generalError: null };

		let errorObj = error;

		// Handle string errors - check if it's a JSON string first
		if (typeof error === 'string') {
			// Try to parse as JSON first
			try {
				errorObj = JSON.parse(error);
			} catch (e) {
				// If JSON parsing fails, treat as plain string (backward compatibility)
				return { fieldErrors: {}, generalError: error };
			}
		}

		// Handle object errors with field-specific messages
		if (typeof errorObj === 'object' && errorObj !== null) {
			const fieldErrors = {};
			let hasFieldErrors = false;

			for (const [fieldName, fieldErrorArray] of Object.entries(errorObj)) {
				if (Array.isArray(fieldErrorArray)) {
					const validErrors = fieldErrorArray.filter((msg) => typeof msg === 'string');
					if (validErrors.length > 0) {
						fieldErrors[fieldName] = validErrors;
						hasFieldErrors = true;
					}
				}
			}

			if (hasFieldErrors) {
				return { fieldErrors, generalError: null };
			}
		}

		return { fieldErrors: {}, generalError: 'An error occurred' };
	}

	// Function to dismiss alerts when input changes
	function dismissAlerts() {
		showErrorMessage = false;
		errorMessage = '';
		fieldErrors = {};
	}

	// Initialize data on mount to ensure businessHourSettings is loaded
	onMount(() => {
		days = businessHourSettings.workShift || [];
		sameAsBusinessHours = true;

		// Store initial state for change detection
		initialDays = JSON.parse(JSON.stringify(days));
		initialSameAsBusinessHours = sameAsBusinessHours;

		// Calculate initial cleaned days
		initialCleanedDays = initialDays.map((d) => ({
			day: d.day,
			active: d.active,
			times: d.active ? d.times : []
		}));
	});

	// When sending, ensure inactive days have empty times
	$: cleanedDays = days.map((d) => ({
		day: d.day,
		active: d.active,
		times: d.active ? d.times : []
	}));

	// Reactive statement to detect changes from initial state
	$: hasChanges =
		JSON.stringify(cleanedDays) !== JSON.stringify(initialCleanedDays) ||
		sameAsBusinessHours !== initialSameAsBusinessHours;

	// Function to reset state with locally captured submission data
	function resetStateWithSubmissionData(submittedDays, submittedSameAsBusinessHours) {
		// Update current form state with the submitted data
		days = JSON.parse(JSON.stringify(submittedDays));
		sameAsBusinessHours = submittedSameAsBusinessHours;

		// Update initial state tracking variables to match the submitted data
		initialDays = JSON.parse(JSON.stringify(submittedDays));
		initialSameAsBusinessHours = submittedSameAsBusinessHours;
		initialCleanedDays = submittedDays.map((d) => ({
			day: d.day,
			active: d.active,
			times: d.active ? d.times : []
		}));
	}

	// Custom enhance function that handles success state reset with local data
	function customEnhance() {
		return async ({ result, update }) => {
			// Capture the form data that will be submitted before processing
			let submissionData = null;
			if (result.type === 'success') {
				// Capture the cleaned data that was submitted to the server
				submissionData = {
					cleanedDays: JSON.parse(JSON.stringify(cleanedDays)),
					sameAsBusinessHours: sameAsBusinessHours
				};
			}

			// Handle the standard enhancement (error handling, toast notifications)
			const standardEnhance = handleEnhance(enhanceOptions);
			await standardEnhance({ result, update });

			// Then, handle success-specific logic for this component
			if (result.type === 'success' && submissionData) {
				// Use locally captured submission data to update form state immediately
				resetStateWithSubmissionData(
					submissionData.cleanedDays,
					submissionData.sameAsBusinessHours
				);
			}
		};
	}

	// Options for handling form enhancement
	$: enhanceOptions = {
		modalOpen: false, // Not a modal component
		setModalOpen: () => {}, // No modal to close
		setShowSuccessMessage: () => {}, // Not used with toast
		setSuccessMessage: () => {}, // Not used with toast
		setShowErrorMessage: (value) => (showErrorMessage = value),
		setErrorMessage: (value) => {
			const parsedResult = parseErrorMessages(value);
			fieldErrors = parsedResult.fieldErrors;
			errorMessage = parsedResult.generalError || '';
			// Set showErrorMessage to true when we have either field errors or general error
			showErrorMessage =
				Object.keys(parsedResult.fieldErrors).length > 0 || parsedResult.generalError !== null;
		},
		// Enhanced success behavior - use toast notifications
		useToastOnSuccess: true,
		closeModalOnSuccess: false // Not a modal component
	};

	// Generate time options every 30 minutes
	const timeOptions = Array.from({ length: 48 }, (_, i) => {
		const hour = Math.floor(i / 2)
			.toString()
			.padStart(2, '0');
		const minutes = i % 2 === 0 ? '00' : '30';
		return `${hour}:${minutes}`;
	});

	// Toggle a day on/off, clearing or defaulting its times
	function toggleDay(index) {
		days[index].active = !days[index].active;
		if (!days[index].active) {
			days[index].times = [];
		} else if (days[index].times.length === 0) {
			// default to 09:00-18:00 when re-activated
			days[index].times = [{ start: '09:00', end: '18:00' }];
		}
		days = [...days]; // trigger reactivity
		dismissAlerts(); // Dismiss alerts when form changes
	}
</script>

<form method="POST" action="?/update_user_work_schedule" use:enhance={customEnhance}>
	<div class="space-y-4 rounded-lg bg-white p-6 shadow-md">
		<!-- Error display -->
		{#if showErrorMessage}
			<Alert color="red" class="mb-4">
				{errorMessage}
			</Alert>
		{/if}
		<!-- Field-specific error display for multi-field errors -->
		{#if Object.keys(fieldErrors).length > 0}
			{#each Object.entries(fieldErrors) as [fieldName, errors]}
				{#each errors as error}
					<Alert color="red" class="mb-4">
						<strong>{fieldName}:</strong>
						{error}
					</Alert>
				{/each}
			{/each}
		{/if}

		<!-- Hidden inputs for server action -->
		<input type="hidden" name="sameAsBusinessHours" value={sameAsBusinessHours} />
		<input type="hidden" name="workShiftData" value={JSON.stringify(cleanedDays)} />

		<div class="flex w-full items-center justify-between">
			<div>
				<h2 class="text-xl font-medium text-gray-700">{t('business_hours')}</h2>
				<p class="text-sm text-gray-600">{t('business_hours_description')}</p>
			</div>
			<Button
				type="submit"
				disabled={!hasChanges}
				class="rounded-lg bg-blue-500 font-medium text-white transition-colors hover:bg-blue-600 disabled:cursor-not-allowed disabled:opacity-50"
			>
				{t('save')}
			</Button>
		</div>

		<div>
			{#if days.length > 0}
				{#each days as day, i}
					<div class="mb-4 flex items-center">
						<div class="w-10">
							<input
								type="checkbox"
								checked={day.active}
								on:change={() => toggleDay(i)}
								class="h-5 w-5 rounded border-gray-300 bg-gray-100 text-blue-500 focus:ring-2 focus:ring-blue-500"
							/>
						</div>
						<label for="day-{i}" class="w-32 font-medium text-gray-700">
							{day.day}
						</label>

						{#if day.active}
							<div class="flex flex-1 items-center">
								<select
									bind:value={day.times[0].start}
									on:change={dismissAlerts}
									class="block w-full appearance-none rounded border border-gray-300 bg-white px-4 py-2 pr-8 leading-tight text-gray-700 focus:border-blue-500 focus:outline-none"
								>
									{#each timeOptions as option}
										<option value={option}>{option}</option>
									{/each}
								</select>

								<span class="mx-3">{t('to')}</span>

								<select
									bind:value={day.times[0].end}
									on:change={dismissAlerts}
									class="block w-full appearance-none rounded border border-gray-300 bg-white px-4 py-2 pr-8 leading-tight text-gray-700 focus:border-blue-500 focus:outline-none"
								>
									{#each timeOptions as option}
										<option value={option}>{option}</option>
									{/each}
								</select>
							</div>
						{/if}
					</div>
				{/each}
			{:else}
				<p class="text-gray-500">Loading business hours...</p>
			{/if}
		</div>
	</div>
</form>
