<script lang="ts">
	import { <PERSON>, <PERSON><PERSON>, <PERSON>ge, Modal } from 'flowbite-svelte';
	import {
		WandMagicSparklesOutline,
		ChartMixedOutline,
		ExclamationCircleOutline,
		StarOutline,
		ClockOutline
	} from 'flowbite-svelte-icons';

	// Data for the insight cards
	const insights = [
		{
			title: 'Claims Processing Analysis',
			description:
				'Excellent performance with 97.28% pass rate. Non-complicate cases dominate at 71%, indicating efficient processing workflows. Recommend maintaining current procedures while monitoring the 29% complicate cases for optimization opportunities.',
			impact: 'High Impact',
			impactColor: 'red',
			icon: ChartMixedOutline,
			iconBgClass: 'bg-blue-100',
			iconClass: 'text-blue-600'
		},
		{
			title: 'SLA Fax Claims Assessment',
			description:
				'72.38% pass rate shows room for improvement. Clean cases represent 75% of total volume. Focus on reducing unclean case processing time and improving documentation quality to boost overall performance.',
			impact: 'Medium Impact',
			impactColor: 'yellow',
			icon: ExclamationCircleOutline,
			iconBgClass: 'bg-red-100',
			iconClass: 'text-red-600'
		},
		{
			title: 'Credit & Reimbursement Optimization',
			description:
				'65.73% pass rate indicates significant optimization potential. March showed concerning 18% pass rate. Implement stricter validation processes and enhanced training to improve credit processing efficiency.',
			impact: 'High Impact',
			impactColor: 'red',
			icon: StarOutline,
			iconBgClass: 'bg-green-100',
			iconClass: 'text-green-600'
		},
		{
			title: 'Renewal Opportunity',
			description:
				"Bangkok Bank's policy is due for renewal in 45 days. Based on their claim history and market conditions, we recommend a 5% premium increase with expanded dental coverage.",
			impact: 'Medium Impact',
			impactColor: 'yellow',
			icon: ClockOutline,
			iconBgClass: 'bg-purple-100',
			iconClass: 'text-purple-600'
		}
	];

	let bvModalOpen = false;

	function openBVModal() {
		bvModalOpen = true;
	}
</script>

<Button size="sm" class="bg-blue-600 text-white hover:bg-blue-700" on:click={() => openBVModal()}>
	BV
</Button>

<Modal bind:open={bvModalOpen} size="lg" title="BV Modal">
	<h2 slot="header">AI Insights & Recommendations</h2>

	<div class="min-h-screen w-full bg-slate-50 p-4 sm:p-8">
		<div class="mx-auto max-w-7xl rounded-2xl bg-white p-6 shadow-sm sm:p-8">
			<!-- Header Section -->
			<div class="mb-8 flex flex-col items-start justify-between sm:flex-row sm:items-center">
				<div class="flex items-center gap-3">
					<WandMagicSparklesOutline class="h-8 w-8 text-primary-600" />
					<div>
						<h1 class="text-2xl font-bold text-gray-800">AI Insights & Recommendations</h1>
						<p class="text-gray-500">AI-powered analysis to help interpret your data</p>
					</div>
				</div>
				<div class="mt-4 w-full sm:mt-0 sm:w-auto">
					<Button outline color="primary" class="w-full sm:w-auto">Generate Custom Analysis</Button>
				</div>
			</div>

			<!-- Insights Grid -->
			<div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
				{#each insights as item}
					<Card padding="xl" class="transition-shadow duration-300 hover:shadow-lg">
						<div class="flex h-full flex-col">
							<div class="flex items-start justify-between gap-4">
								<div class="flex items-start gap-4">
									<div
										class="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full {item.iconBgClass}"
									>
										<svelte:component this={item.icon} class="h-7 w-7 {item.iconClass}" />
									</div>
									<div>
										<h3 class="text-lg font-bold text-gray-900">{item.title}</h3>
									</div>
								</div>
								<Badge color={item.impactColor} large class="flex-shrink-0">{item.impact}</Badge>
							</div>

							<p class="mt-3 flex-grow leading-relaxed text-gray-600">{item.description}</p>

							<div class="mt-6 border-t border-gray-200 pt-4">
								<div class="flex items-center gap-2 text-sm font-medium text-primary-700">
									<a href="/" class="hover:underline">Create Memo</a>
									<span class="text-gray-300">|</span>
									<a href="/" class="hover:underline">Schedule Meeting</a>
									<span class="text-gray-300">|</span>
									<a href="/" class="hover:underline">Detailed Analysis</a>
								</div>
							</div>
						</div>
					</Card>
				{/each}
			</div>
		</div>
	</div>



    
</Modal>
