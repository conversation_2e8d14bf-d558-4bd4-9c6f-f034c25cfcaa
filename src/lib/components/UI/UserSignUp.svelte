<script lang="ts">
	import { t } from '$lib/stores/i18n';

	import { enhance } from '$app/forms';
	import { Button, Modal, Label, Input, Alert, Select, Textarea } from 'flowbite-svelte';
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';

	// export let user: any;

	let signUpForm: HTMLFormElement;
	let signUpModalOpen = false;
	// let selectInstances: any = null;

	// State variables for handling messages
	let showSuccessMessage = false;
	let showErrorMessage = false;
	let successMessage = '';
	let errorMessage = '';
	let fieldErrors: Record<string, string[]> = {};

	// Flag to track if form should be reset when modal closes
	let shouldResetOnClose = true;

	// Tab navigation state
	let activeTab = 0;

	const languageOptions = [
		{ value: 'en', name: t('language_name_en') },
		{ value: 'th', name: t('language_name_th') }
	];

	// Function to dismiss all error alerts when user starts typing
	function dismissAlerts() {
		showErrorMessage = false;
		errorMessage = '';
		fieldErrors = {};
	}

	// Validation errors for contact fields (separate from server errors)
	let validationErrors = {
		personal_phone: '',
		personal_email: '',
		work_phone: '',
		work_email: ''
	};

	// Phone validation functions (similar to UserProfile.svelte)
	function filterPhoneInput(value: string) {
		// Only allow digits and plus sign
		return value.replace(/[^0-9+]/g, '');
	}

	function validatePhoneNumber(phone: string) {
		if (!phone.trim()) return { isValid: true, error: '' }; // Optional field
		if (phone.length > 20)
			return { isValid: false, error: 'Phone number must be 20 characters or less' };
		return { isValid: true, error: '' };
	}

	// Email validation function
	function validateEmail(email: string) {
		if (!email.trim()) return { isValid: true, error: '' }; // Optional field
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		if (!emailRegex.test(email)) {
			return { isValid: false, error: 'Please enter a valid email address' };
		}
		return { isValid: true, error: '' };
	}

	// Phone input handler with filtering and validation
	function handlePhoneInput(event: Event, fieldName: 'personal_phone' | 'work_phone') {
		const target = event.target as HTMLInputElement;
		const filteredValue = filterPhoneInput(target.value);

		// Update form data with filtered value
		formData[fieldName] = filteredValue;

		// Validate and update error state
		const validation = validatePhoneNumber(filteredValue);
		validationErrors[fieldName] = validation.error;

		// Also dismiss server alerts
		dismissAlerts();
	}

	// Email input handler with validation
	function handleEmailInput(event: Event, fieldName: 'personal_email' | 'work_email') {
		const target = event.target as HTMLInputElement;

		// Validate and update error state
		const validation = validateEmail(target.value);
		validationErrors[fieldName] = validation.error;

		// Also dismiss server alerts
		dismissAlerts();
	}

	// Function to reset form to initial state
	function resetForm() {
		formData = {
			// Basic Info (Required)
			employee_id: String(count + 1),
			username: '',
			password: '',
			confirm_password: '',
			name: '',
			first_name: '',
			last_name: '',
			// department: '',
			// role: '',
			is_active: true,
			is_staff: true,
			is_superuser: false,

			// Contact Info (Optional)
			personal_phone: '',
			work_phone: '',
			personal_email: '',
			work_email: '',

			// Preferences (Optional)
			preferred_language: '',

			// Emergency Contact (Optional)
			emergency_contact_name: '',
			emergency_contact_phone: '',
			emergency_contact_email: '',
		};
		passwordFieldsEverTyped = false;
		activeTab = 0; // Reset to first tab

		// Reset validation errors
		validationErrors = {
			personal_phone: '',
			personal_email: '',
			work_phone: '',
			work_email: ''
		};
	}

	// Combined handler for password input (existing functionality + alert dismissal)
	function handlePasswordInput() {
		passwordFieldsEverTyped = true;
		dismissAlerts();
	}

	// Tab navigation functions
	function nextTab() {
		if (activeTab < 3) {
			activeTab++;
		}
	}

	function previousTab() {
		if (activeTab > 0) {
			activeTab--;
		}
	}

	function goToTab(tabIndex: number) {
		if (tabIndex >= 0 && tabIndex <= 3) {
			activeTab = tabIndex;
		}
	}

	// function openSignUpModal(user: any) {
	function openSignUpModal() {
		// selectInstances = { ...user };
		signUpModalOpen = true;
		// Reset messages when opening modal
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
		fieldErrors = {};
		passwordFieldsEverTyped = false;
		// Reset the flag when opening modal
		shouldResetOnClose = true;
	}

	function parseErrorMessages(error: any): {
		fieldErrors: Record<string, string[]>;
		generalError: string | null;
	} {
		if (!error) return { fieldErrors: {}, generalError: null };

		let errorObj = error;

		// Handle string errors - check if it's a JSON string first
		if (typeof error === 'string') {
			// Try to parse as JSON first
			try {
				errorObj = JSON.parse(error);
			} catch (e) {
				// If JSON parsing fails, treat as plain string (backward compatibility)
				return { fieldErrors: {}, generalError: error };
			}
		}

		// Handle object errors with field-specific messages
		if (typeof errorObj === 'object' && errorObj !== null) {
			const fieldErrors: Record<string, string[]> = {};
			let hasFieldErrors = false;

			for (const [fieldName, fieldErrorArray] of Object.entries(errorObj)) {
				if (Array.isArray(fieldErrorArray)) {
					const validErrors = fieldErrorArray.filter((msg) => typeof msg === 'string');
					if (validErrors.length > 0) {
						fieldErrors[fieldName] = validErrors;
						hasFieldErrors = true;
					}
				}
			}

			if (hasFieldErrors) {
				return { fieldErrors, generalError: null };
			}
		}

		return { fieldErrors: {}, generalError: 'An error occurred' };
	}

	function handleSignUpSubmit(event: Event) {
		// Reset messages
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
		fieldErrors = {};

		return true;
	}

	$: enhanceOptions = {
		modalOpen: signUpModalOpen,
		setModalOpen: (value: boolean) => {
			signUpModalOpen = value;
			// If modal is closing and we should reset form, do it now
			if (!value && shouldResetOnClose) {
				resetForm();
			}
		},
		setShowSuccessMessage: (value: boolean) => (showSuccessMessage = value),
		setSuccessMessage: (value: string) => {
			successMessage = value;
			// Set flag to reset form when modal closes after successful submission
			shouldResetOnClose = true;
		},
		setShowErrorMessage: (value: boolean) => (showErrorMessage = value),
		setErrorMessage: (value: string) => {
			const parsedResult = parseErrorMessages(value);
			fieldErrors = parsedResult.fieldErrors;
			errorMessage = parsedResult.generalError || '';
			// Set showErrorMessage to true when we have either field errors or general error
			showErrorMessage =
				Object.keys(parsedResult.fieldErrors).length > 0 || parsedResult.generalError !== null;
		},
		// New properties for enhanced success behavior
		useToastOnSuccess: true,
		closeModalOnSuccess: true
	};
	export let count: number;

	// Form data with initial values from user
	let formData = {
		// Basic Info (Required)
		username: '',
		password: '',
		confirm_password: '',
		name: '',
		email: '',
		// employee_id: "",
		employee_id: String(count + 1),
		first_name: '',
		last_name: '',
		department: '',
		role: '',
		is_active: true,
		is_staff: true,
		is_superuser: false,

		// Contact Info (Optional)
		personal_phone: '',
		work_phone: '',
		personal_email: '',
		work_email: '',

		// Preferences (Optional)
		preferred_language: '',

		// Emergency Contact (Optional)
		emergency_contact_name: '',
		emergency_contact_phone: '',
		emergency_contact_email: '',
	};

	// Password validation (similar to UserProfile)
	const specialChars = '!@#$%^&*';
	let passwordFieldsEverTyped = false;

	function checkPasswordRules(password: string) {
		return {
			length: password.length > 8,
			lowercase: /[a-z]/.test(password),
			uppercase: /[A-Z]/.test(password),
			special: new RegExp(`[${specialChars.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&')}]`).test(
				password
			),
			number: /[0-9]/.test(password)
		};
	}

	$: passwordRulesStatus = checkPasswordRules(formData.password);
	$: allPasswordRulesPassed = Object.values(passwordRulesStatus).every((value) => value === true);
	$: passwordsMatch =
		formData.password === formData.confirm_password && formData.password.length > 0;

	// Step validation logic
	$: step1Valid =
		formData.name.trim() !== '' &&
		formData.first_name.trim() !== '' &&
		formData.last_name.trim() !== '' &&
		formData.username.trim() !== '' &&
		allPasswordRulesPassed &&
		passwordsMatch;

	// Check if there are any contact validation errors
	$: hasContactValidationErrors = Object.values(validationErrors).some((error) => error !== '');

	// Update step2Valid to include validation check
	$: step2Valid = formData.work_email.trim() !== '' && !hasContactValidationErrors;

	$: step3Valid = true; // All optional fields

	$: step4Valid = true; // All optional fields

	// Stepper steps configuration
	$: stepperSteps = [
		{
			id: 1,
			label: t('signup_form_tab_personal_info'),
			description: '',
			status: activeTab === 0 ? 'current' : step1Valid ? 'completed' : 'pending',
			type: 'required'
		},
		{
			id: 2,
			label: t('signup_form_tab_contact_details'),
			description: '',
			status: activeTab === 1 ? 'current' : activeTab > 1 ? 'completed' : 'pending',
			type: 'optional'
		},
		{
			id: 3,
			label: t('signup_form_tab_preference'),
			description: '',
			status: activeTab === 2 ? 'current' : activeTab > 2 ? 'completed' : 'pending',
			type: 'optional'
		},
		{
			id: 4,
			label: t('signup_form_tab_emergency_contact'),
			description: '',
			status: activeTab === 3 ? 'current' : activeTab > 3 ? 'completed' : 'pending',
			type: 'optional'
		}
	];

	// Handle stepper step clicks
	function handleStepClick(stepId: number) {
		goToTab(stepId - 1);
	}
</script>

<Button
	size="sm"
	class="bg-green-600 text-white hover:bg-green-700"
	on:click={() => openSignUpModal()}
>
	+ {t('new_account')}
</Button>

<Modal bind:open={signUpModalOpen} size="lg" title="Sign-up User Information">
	<h3 slot="header">{t('create_account')}</h3>
	{#if showSuccessMessage}
		<Alert color="green" class="mb-4">
			{successMessage}
		</Alert>
	{/if}
	{#if showErrorMessage && errorMessage}
		<Alert color="red" class="mb-4">
			{errorMessage}
		</Alert>
	{/if}

	<!-- Stepper Navigation -->
	<div class="mb-6">
		<div class="relative">
			<!-- Continuous Connector Line Background -->
			<div class="absolute left-5 right-5 top-5 h-0.5 bg-gray-300"
				 style="left: calc(12.5%); width: calc(75%);}"
			></div>

			<!-- Progress Line (shows completion progress) -->
			<div
				class="absolute top-5 h-0.5 bg-green-500 transition-all duration-300"
				style="left: calc(12.5%); width: {activeTab > 0 ? `${(activeTab / 3) * 75}%` : '0%'}"
			></div>

			<!-- Step Buttons Grid -->
			<div class="relative z-10 grid grid-cols-4 gap-4">
				{#each stepperSteps as step}
					<div class="flex flex-col items-center">
						<!-- Step Circle -->
						<button
							type="button"
							class="flex h-10 w-10 items-center justify-center rounded-full border-2 text-sm font-semibold transition-colors
								{step.status === 'completed'
								? 'border-green-500 bg-green-500 text-white hover:bg-green-600'
								: step.status === 'current'
									? 'border-blue-500 bg-blue-500 text-white'
									: 'border-gray-300 bg-white text-gray-500 hover:border-gray-400'}"
							on:click={() => handleStepClick(step.id)}
							aria-label="Go to {step.label}"
						>
							{#if step.status === 'completed'}
								<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
									<path
										fill-rule="evenodd"
										d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
										clip-rule="evenodd"
									/>
								</svg>
							{:else}
								{step.id}
							{/if}
						</button>

						<!-- Step Label (Hidden on mobile) -->
						<div class="mt-2 hidden text-center sm:hidden md:block lg:block">
							<div
								class="text-sm font-medium {step.status === 'current'
									? 'text-blue-600'
									: step.status === 'completed'
										? 'text-green-600'
										: 'text-gray-500'}"
							>
								<p class="leading-tight">{step.label}</p>
							</div>
							{#if step.description}
								<div class="mt-1 text-xs text-gray-400">{step.description}</div>
							{/if}
						</div>
					</div>
				{/each}
			</div>
		</div>

		<!-- Mobile Step Label -->
		<div class="mt-4 text-center sm:hidden">
			<div class="text-sm font-medium text-blue-600">{stepperSteps[activeTab].label}</div>
			{#if stepperSteps[activeTab].description}
				<div class="mt-1 text-xs text-gray-400">{stepperSteps[activeTab].description}</div>
			{/if}
		</div>
	</div>

	<!-- Tab Content Indicators -->
	{#if activeTab === 0}
		<div class="mb-4 flex items-center gap-2">
			<span class="rounded bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
				{t('signup_form_required_badge')}</span
			>
		</div>
	{:else if activeTab === 1}
		<div class="mb-4 flex items-center gap-2">
			<span class="rounded bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800"
				>{t('signup_form_optional_badge')}</span
			>
		</div>
	{:else if activeTab === 2}
		<div class="mb-4 flex items-center gap-2">
			<span class="rounded bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800"
				>{t('signup_form_optional_badge')}</span
			>
		</div>
	{:else if activeTab === 3}
		<div class="mb-4 flex items-center gap-2">
			<span class="rounded bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800"
				>{t('signup_form_optional_badge')}</span
			>
		</div>
	{/if}

	<form
		bind:this={signUpForm}
		action="?/sign_up_user"
		method="POST"
		use:enhance={() => handleEnhance(enhanceOptions)}
		class="h-[400px] space-y-4 overflow-y-auto"
		on:submit={handleSignUpSubmit}
	>
		<!-- Basic Info Tab Content -->
		{#if activeTab === 0}
			<div>
				<Label for="employee_id" class="space-y-2">
					{t('employee_id')}
				</Label>
				<Input
					id="employee_id"
					name="employee_id"
					type="text"
					bind:value={formData.employee_id}
					required
					disabled
					readonly
				/>
			</div>

			<div class="grid grid-cols-2 gap-4">
				<div>
					<Label for="username" class="space-y-2">
						{t('username')}
					</Label>
					<Input
						id="username"
						name="username"
						type="text"
						maxlength={20}
						bind:value={formData.username}
						on:input={dismissAlerts}
						required
					/>
					{#if fieldErrors.username}
						{#each fieldErrors.username as error}
							<Alert color="red" class="mt-1 px-3 py-2 text-sm">
								<!-- {error} -->
								{t('signup_error_duplicated_username')}
							</Alert>
						{/each}
					{/if}
				</div>
				<div>
					<Label for="name" class="space-y-2">
						{t('nickname')}
					</Label>
					<Input
						id="name"
						name="name"
						type="text"
						maxlength={20}
						bind:value={formData.name}
						on:input={dismissAlerts}
						required
					/>
				</div>
			</div>

			<div class="grid grid-cols-2 gap-4">
				<div>
					<Label for="first_name" class="space-y-2">
						{t('first_name')}
					</Label>
					<Input
						id="first_name"
						name="first_name"
						type="text"
						maxlength={50}
						bind:value={formData.first_name}
						on:input={dismissAlerts}
						required
					/>
					{#if fieldErrors.first_name}
						{#each fieldErrors.first_name as error}
							<Alert color="red" class="mt-1 px-3 py-2 text-sm">
								{error}
							</Alert>
						{/each}
					{/if}
				</div>

				<div>
					<Label for="last_name" class="space-y-2">
						{t('last_name')}
					</Label>
					<Input
						id="last_name"
						name="last_name"
						type="text"
						maxlength={50}
						bind:value={formData.last_name}
						on:input={dismissAlerts}
						required
					/>
					{#if fieldErrors.last_name}
						{#each fieldErrors.last_name as error}
							<Alert color="red" class="mt-1 px-3 py-2 text-sm">
								{error}
							</Alert>
						{/each}
					{/if}
				</div>
			</div>

			<div class="grid grid-cols-2 gap-4">
				<div>
					<Label for="password" class="space-y-2">
						{t('password')}
					</Label>
					<Input
						id="password"
						name="password"
						type="password"
						maxlength={30}
						bind:value={formData.password}
						on:input={handlePasswordInput}
						required
					/>
					{#if fieldErrors.password}
						{#each fieldErrors.password as error}
							<Alert color="red" class="mt-1 px-3 py-2 text-sm">
								{error}
							</Alert>
						{/each}
					{/if}

					<div class="mb-1 mt-2 text-xs font-normal text-gray-400">
						{t('password_validation_msg_1')}
					</div>
					<ul class="space-y-0 text-xs">
						<li class="flex items-center">
							<span
								class={passwordRulesStatus.length
									? 'text-green-600'
									: passwordFieldsEverTyped
										? 'text-red-600'
										: 'text-gray-400'}>{t('password_validation_msg_2')}</span
							>
						</li>
						<li class="flex items-center">
							<span
								class={passwordRulesStatus.lowercase && passwordRulesStatus.uppercase
									? 'text-green-600'
									: passwordFieldsEverTyped
										? 'text-red-600'
										: 'text-gray-400'}>{t('password_validation_msg_3')}</span
							>
						</li>
						<li class="flex items-center">
							<span
								class={passwordRulesStatus.number
									? 'text-green-600'
									: passwordFieldsEverTyped
										? 'text-red-600'
										: 'text-gray-400'}>{t('password_validation_msg_4')}</span
							>
						</li>
						<li class="flex items-center">
							<span
								class={passwordRulesStatus.special
									? 'text-green-600'
									: passwordFieldsEverTyped
										? 'text-red-600'
										: 'text-gray-400'}>{t('password_validation_msg_5')} ({specialChars})</span
							>
						</li>
					</ul>
				</div>

				<div>
					<Label for="confirm_password" class="space-y-2">
						{t('confirm_password')}
					</Label>
					<Input
						id="confirm_password"
						name="confirm_password"
						type="password"
						maxlength={30}
						bind:value={formData.confirm_password}
						on:input={handlePasswordInput}
						required
					/>
					<div style="min-height:1em;" class="justify-left items-top mt-2 flex">
						{#if passwordFieldsEverTyped && !passwordsMatch && formData.confirm_password.length > 0}
							<span class="text-left text-xs text-red-600"
								>{t('password_validation_msg_do_not_match')}</span
							>
						{/if}
					</div>
					{#if fieldErrors.confirm_password}
						{#each fieldErrors.confirm_password as error}
							<Alert color="red" class="mt-1 px-3 py-2 text-sm">
								{error}
							</Alert>
						{/each}
					{/if}
				</div>
			</div>

			<!-- <div>
				<Label for="department" class="space-y-2">Department</Label>
				<Input
					id="department"
					name="department"
					type="text"
					bind:value={formData.department}
				/>
			</div>

			<div>
				<Label for="role" class="space-y-2">Role</Label>
				<Select 
					id="role" 
					name="role" 
					bind:value={formData.role}
					required
				>
					{#each roles as role}
						<option value={role}>{role}</option>
					{/each}
				</Select>
			</div> -->

			<!-- <Checkbox class="flex items-center gap-2" bind:checked={formData.is_active} bind:value={formData.is_active}> -->
			<!-- <Checkbox class="flex items-center gap-2" bind:checked={formData.is_active">
				Enable Active Status
			</Checkbox>
			<input type="hidden" name="is_active" value={formData.is_active}> -->

			<!-- <Checkbox class="flex items-center gap-2" bind:checked={formData.is_staff} bind:value={formData.is_staff}> -->
			<!-- <Checkbox class="flex items-center gap-2" bind:checked={formData.is_staff">
				Enable Staff
			</Checkbox>                 -->
			<!-- <input type="hidden" name="is_staff" value={formData.is_staff}> -->

			<!-- <Checkbox class="flex items-center gap-2" bind:checked={formData.is_superuser} bind:value={formData.is_superuser}> -->
			<!-- <Checkbox class="flex items-center gap-2" bind:checked={formData.is_superuser">
				Enable Superuser
			</Checkbox>
			<input type="hidden" name="is_superuser" value={formData.is_superuser}> -->
		{/if}

		<!-- Contact Tab Content -->
		{#if activeTab === 1}
			<div class="grid grid-cols-2 gap-4">
				<div>
					<Label for="personal_phone" class="space-y-2">{t('signup_form_personal_phone')}</Label>
					<Input
						id="personal_phone"
						name="personal_phone"
						type="text"
						maxlength={20}
						value={formData.personal_phone}
						on:input={(e) => handlePhoneInput(e, 'personal_phone')}
					/>
					{#if validationErrors.personal_phone}
						<Alert color="red" class="mt-1 px-3 py-2 text-sm">
							{validationErrors.personal_phone}
						</Alert>
					{/if}
				</div>

				<div>
					<Label for="personal_email" class="space-y-2">{t('signup_form_personal_email')}</Label
					>
					<Input
						id="personal_email"
						name="personal_email"
						type="email"
						maxlength={30}
						bind:value={formData.personal_email}
						on:input={(e) => handleEmailInput(e, 'personal_email')}
					/>
					{#if validationErrors.personal_email}
						<Alert color="red" class="mt-1 px-3 py-2 text-sm">
							{validationErrors.personal_email}
						</Alert>
					{/if}
				</div>
			</div>

			<div class="grid grid-cols-2 gap-4">
				<div>
					<Label for="work_phone" class="space-y-2">{t('signup_form_work_phone')}</Label>
					<Input
						id="work_phone"
						name="work_phone"
						type="text"
						maxlength={20}
						value={formData.work_phone}
						on:input={(e) => handlePhoneInput(e, 'work_phone')}
					/>
					{#if validationErrors.work_phone}
						<Alert color="red" class="mt-1 px-3 py-2 text-sm">
							{validationErrors.work_phone}
						</Alert>
					{/if}
				</div>

				<div>
					<Label for="work_email" class="space-y-2">{t('signup_form_work_email')}</Label>
					<Input
						id="work_email"
						name="work_email"
						type="email"
						maxlength={30}
						bind:value={formData.work_email}
						on:input={(e) => handleEmailInput(e, 'work_email')}
						required
					/>
					{#if validationErrors.work_email}
						<Alert color="red" class="mt-1 px-3 py-2 text-sm">
							{validationErrors.work_email}
						</Alert>
					{/if}
					{#if fieldErrors.email}
						{#each fieldErrors.email as error}
							<Alert color="red" class="mt-1 px-3 py-2 text-sm">
								<!-- {error} -->
								{t('signup_error_duplicated_email')}
							</Alert>
						{/each}
					{/if}
				</div>
			</div>
		{/if}

		<!-- Preferences Tab Content -->
		{#if activeTab === 2}
			<div>
				<Label for="preferred_language" class="space-y-2"
					>{t('signup_form_preferred_language')}</Label
				>
				<Select
					id="preferred_language"
					name="preferred_language"
					bind:value={formData.preferred_language}
					items={languageOptions}
					on:change={dismissAlerts}
					placeholder={t('signup_form_preferred_language_placeholder')}
				/>
			</div>
		{/if}

		<!-- Emergency Tab Content -->
		{#if activeTab === 3}
			<div>
				<Label for="emergency_contact_name" class="space-y-2"
					>{t('signup_form_emergency_name')}</Label
				>
				<Input
					id="emergency_contact_name"
					name="emergency_contact_name"
					type="text"
					maxlength={50}
					bind:value={formData.emergency_contact_name}
					on:input={dismissAlerts}
				/>
			</div>

			<div class="grid grid-cols-2 gap-4">
				<div>
					<Label for="emergency_contact_phone" class="space-y-2"
						>{t('signup_form_emergency_phone')}</Label
					>
					<Input
						id="emergency_contact_phone"
						name="emergency_contact_phone"
						type="tel"
						maxlength={20}
						bind:value={formData.emergency_contact_phone}
						on:input={dismissAlerts}
					/>
				</div>

				<div>
					<Label for="emergency_contact_email" class="space-y-2"
						>{t('signup_form_emergency_email')}</Label
					>
					<Input
						id="emergency_contact_email"
						name="emergency_contact_email"
						type="email"
						maxlength={30}
						bind:value={formData.emergency_contact_email}
						on:input={dismissAlerts}
					/>
				</div>
			</div>
		{/if}

		<!-- Always include hidden fields -->
		<input type="hidden" name="is_active" value="true" />
		<input type="hidden" name="is_staff" value="true" />
		<input type="hidden" name="is_superuser" value="false" />
	</form>
	<svelte:fragment slot="footer">
		<div class="flex w-full justify-between">
			<div class="flex gap-2">
				<Button color="alternative" on:click={() => (signUpModalOpen = false)}>{t('cancel')}</Button
				>
			</div>

			<div class="flex gap-2">
				{#if activeTab < 3}
					{#if activeTab > 0}
						<Button color="light" on:click={previousTab}>{t('previous')}</Button>
					{/if}
					<Button color="blue" on:click={nextTab}>{t('next')}</Button>
				{:else}
					<Button color="light" on:click={previousTab}>{t('previous')}</Button>
					<Button
						type="submit"
						color="blue"
						disabled={!allPasswordRulesPassed || !passwordsMatch}
						on:click={() => signUpForm.requestSubmit()}
					>
						{t('create_account')}
					</Button>
				{/if}
			</div>
		</div>
	</svelte:fragment>
</Modal>
